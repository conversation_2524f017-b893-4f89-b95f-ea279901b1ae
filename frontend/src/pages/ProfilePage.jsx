import { useState, useEffect, useRef } from 'react';
import { Link, Navigate } from 'react-router-dom';
import PropTypes from 'prop-types';
import { 
  FaUser, FaGamepad, FaCog, FaHeart, FaHistory, 
  FaSignOutAlt, FaCreditCard, FaShoppingCart, FaUpload, 
  FaChevronRight, FaEdit
} from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import GameCard from '../components/GameCard';
import { API_URL } from '../config/env.js';
import { gamePlaceholder } from '../assets/placeholders.js';
import { getSecureImageUrl } from '../utils/imageUtils';

const ProfilePage = () => {
  const { user, logout } = useAuth();
  const [activeSection, setActiveSection] = useState('overview');
  const [games, setGames] = useState([]);
  const [wishlist, setWishlist] = useState([]);
  const [loading, setLoading] = useState(true);
  const [credits, setCredits] = useState(0);
  const [mobileNavOpen, setMobileNavOpen] = useState(false);
  const [userData, setUserData] = useState(null); // Added to store complete user data

  useEffect(() => {
    if (user) {
      // Fetch the complete user profile including bio
      const fetchUserProfile = async () => {
        try {
          const response = await fetch('/api/users/profile', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });
          
          if (response.ok) {
            const profileData = await response.json();
            setUserData(profileData);
          } else {
            console.error('Failed to fetch user profile');
          }
        } catch (error) {
          console.error('Error fetching user profile:', error);
        }
      };

      // Fetch user data
      fetchUserProfile();
      
      // Simulate fetching game data from API (keep existing code)
      setTimeout(() => {
        setGames(sampleGames);
        setWishlist(sampleWishlist);
        setCredits(350); // Sample credits
        setLoading(false);
      }, 800);
    }
  }, [user]);

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  const handleLogout = () => {
    if (window.confirm('Are you sure you want to log out?')) {
      logout();
    }
  };

  const toggleMobileNav = () => {
    setMobileNavOpen(!mobileNavOpen);
  };

  // Render the content based on active section
  const renderContent = () => {
    switch(activeSection) {
      case 'overview':
        return <OverviewSection user={userData || user} games={games} credits={credits} setActiveSection={setActiveSection} />;
      case 'library':
        return <LibrarySection games={games} />;
      case 'settings':
        return <SettingsSection user={userData || user} />;
      case 'wishlist':
        return <WishlistSection wishlist={wishlist} />;
      case 'purchases':
        return <PurchasesSection />;
      case 'uploads':
        return <UploadsSection />;
      default:
        return <OverviewSection user={userData || user} games={games} credits={credits} setActiveSection={setActiveSection} />;
    }
  };

  return (
    <div className="min-h-[calc(100vh-60px)] bg-gray-900 p-5 text-gray-200">
      <div className="max-w-7xl mx-auto flex gap-8 relative">
        {/* Mobile Navigation Toggle */}
        <button 
          className="hidden max-lg:block w-full bg-gray-800 text-white p-4 border-none text-left text-lg font-semibold cursor-pointer rounded mb-5 relative"
          onClick={toggleMobileNav}
        >
          {activeSection} 
          <FaChevronRight className={`absolute right-4 transition-transform duration-300 ${mobileNavOpen ? 'rotate-90' : ''}`} />
        </button>

        {/* Sidebar Navigation */}
        <aside className={`w-72 bg-gray-800 rounded-lg shadow-xl p-5 sticky top-20 h-fit max-lg:fixed max-lg:top-0 max-lg:left-0 max-lg:h-full max-lg:z-50 max-lg:transform max-lg:transition-transform max-lg:duration-300 ${mobileNavOpen ? 'max-lg:translate-x-0' : 'max-lg:-translate-x-full'}`}>
          <div className="flex flex-col items-center py-5 border-b border-gray-600">
            <div className="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center mb-4 overflow-hidden border-2 border-gray-600">
              {user.avatar ? (
                <img src={getSecureImageUrl(user.avatar)} alt={user.username} className="w-full h-full object-cover" />
              ) : (
                <FaUser className="text-5xl text-gray-500" />
              )}
            </div>
            <h2 className="text-2xl font-bold text-white mb-1">{user.username}</h2>
            <div className="flex items-center gap-2 bg-yellow-500/15 text-yellow-400 px-3 py-1 rounded-full font-semibold mt-2">
              <FaCreditCard /> {credits} Credits
            </div>
          </div>
          
          <nav className="mt-5">
            <ul className="list-none p-0">
              <li className="mb-1">
                <button 
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'overview' 
                      ? 'bg-red-500/15 text-red-400 font-semibold' 
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('overview'); setMobileNavOpen(false);}}
                >
                  <FaUser /> Overview
                </button>
              </li>
              <li className="mb-1">
                <button 
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'library' 
                      ? 'bg-red-500/15 text-red-400 font-semibold' 
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('library'); setMobileNavOpen(false);}}
                >
                  <FaGamepad /> Game Library
                </button>
              </li>
              <li className="mb-1">
                <button 
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'wishlist' 
                      ? 'bg-red-500/15 text-red-400 font-semibold' 
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('wishlist'); setMobileNavOpen(false);}}
                >
                  <FaHeart /> Wishlist
                </button>
              </li>
              <li className="mb-1">
                <button 
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'purchases' 
                      ? 'bg-red-500/15 text-red-400 font-semibold' 
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('purchases'); setMobileNavOpen(false);}}
                >
                  <FaHistory /> Purchase History
                </button>
              </li>
              <li className="mb-1">
                <button 
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'uploads' 
                      ? 'bg-red-500/15 text-red-400 font-semibold' 
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('uploads'); setMobileNavOpen(false);}}
                >
                  <FaShoppingCart /> My Uploads
                </button>
              </li>
              <li className="mb-1">
                <button 
                  className={`flex items-center gap-3 w-full p-3 border-none text-left text-base rounded-md cursor-pointer transition-all duration-200 ${
                    activeSection === 'settings' 
                      ? 'bg-red-500/15 text-red-400 font-semibold' 
                      : 'bg-transparent text-gray-300 hover:bg-white/5 hover:text-red-400'
                  }`}
                  onClick={() => {setActiveSection('settings'); setMobileNavOpen(false);}}
                >
                  <FaCog /> Settings
                </button>
              </li>
              <li className="mt-3">
                <button 
                  className="flex items-center gap-3 w-full p-3 border-none bg-transparent text-red-400 text-left text-base rounded-md cursor-pointer transition-all duration-200 hover:bg-white/5"
                  onClick={handleLogout}
                >
                  <FaSignOutAlt /> Logout
                </button>
              </li>
            </ul>
          </nav>
          
          <div className="mt-8 pt-5 border-t border-gray-600 text-center text-gray-500 text-sm">
            <p>Member since {formatDate(user.joinDate || new Date())}</p>
            <Link 
              to="/buy-credits" 
              className="inline-block bg-gradient-to-r from-red-500 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-semibold mt-3 transition-all duration-300 shadow-lg shadow-red-500/30 hover:-translate-y-0.5 hover:shadow-red-500/40 no-underline"
            >
              Buy Credits
            </Link>
          </div>
        </aside>

        {/* Main Content Area */}
        <main className="flex-1 bg-gray-800 rounded-lg shadow-xl p-6 min-h-[600px]">
          {loading ? (
            <div className="w-full flex flex-col items-center justify-center py-24">
              <div className="w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full animate-spin mb-4"></div>
              <p className="text-gray-300">Loading your profile data...</p>
            </div>
          ) : (
            renderContent()
          )}
        </main>
      </div>
    </div>
  );
};

// Overview Section Component
const OverviewSection = ({ user, games, credits, setActiveSection }) => {
  const recentGames = games.slice(0, 3);
  
  return (
    <section className="space-y-8">
      <h1 className="text-3xl font-bold text-white mb-6">Welcome, {user.username}!</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-700/50 p-6 rounded-lg border border-gray-600 hover:border-gray-500 transition-colors duration-200">
          <FaGamepad className="text-3xl text-red-400 mb-4" />
          <div>
            <h3 className="text-lg font-semibold text-gray-200 mb-2">Game Library</h3>
            <p className="text-3xl font-bold text-white mb-1">{games.length}</p>
            <p className="text-gray-400 text-sm">games in collection</p>
          </div>
        </div>
        
        <div className="bg-gray-700/50 p-6 rounded-lg border border-gray-600 hover:border-gray-500 transition-colors duration-200">
          <FaCreditCard className="text-3xl text-yellow-400 mb-4" />
          <div>
            <h3 className="text-lg font-semibold text-gray-200 mb-2">Credits Balance</h3>
            <p className="text-3xl font-bold text-white mb-1">{credits}</p>
            <p className="text-gray-400 text-sm">available credits</p>
          </div>
        </div>
      </div>
      
      {recentGames.length > 0 && (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-white">Recently Played</h2>
            <button 
              onClick={() => setActiveSection('library')} 
              className="text-red-400 hover:text-red-300 font-medium transition-colors duration-200"
            >
              View All
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recentGames.map(game => (
              <GameCard key={game.id} game={game} />
            ))}
          </div>
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link 
          to="/browse" 
          className="bg-gray-700/50 p-6 rounded-lg border border-gray-600 hover:border-red-400 transition-all duration-200 hover:-translate-y-1 no-underline text-gray-200 hover:text-white"
        >
          <FaGamepad className="text-3xl text-red-400 mb-4" />
          <div>
            <h3 className="text-lg font-semibold mb-2">Browse Games</h3>
            <p className="text-gray-400 text-sm">Find new games to play</p>
          </div>
        </Link>
        
        <Link 
          to="/upload-game" 
          className="bg-gray-700/50 p-6 rounded-lg border border-gray-600 hover:border-red-400 transition-all duration-200 hover:-translate-y-1 no-underline text-gray-200 hover:text-white"
        >
          <FaShoppingCart className="text-3xl text-red-400 mb-4" />
          <div>
            <h3 className="text-lg font-semibold mb-2">Upload Game</h3>
            <p className="text-gray-400 text-sm">Share your game with others</p>
          </div>
        </Link>
        
        <Link 
          to="/buy-credits" 
          className="bg-gray-700/50 p-6 rounded-lg border border-gray-600 hover:border-red-400 transition-all duration-200 hover:-translate-y-1 no-underline text-gray-200 hover:text-white"
        >
          <FaCreditCard className="text-3xl text-yellow-400 mb-4" />
          <div>
            <h3 className="text-lg font-semibold mb-2">Buy Credits</h3>
            <p className="text-gray-400 text-sm">Add credits to your account</p>
          </div>
        </Link>
      </div>
    </section>
  );
};

OverviewSection.propTypes = {
  user: PropTypes.object.isRequired,
  games: PropTypes.array.isRequired,
  credits: PropTypes.number.isRequired,
  setActiveSection: PropTypes.func.isRequired
};

// Library Section Component
const LibrarySection = ({ games }) => {
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  
  // Filter games based on filter and search term
  const filteredGames = games.filter(game => {
    if (filter !== 'all' && game.genre !== filter) return false;
    if (searchTerm && !game.title.toLowerCase().includes(searchTerm.toLowerCase())) return false;
    return true;
  });
  
  return (
    <section className="space-y-6">
      <h1 className="text-3xl font-bold text-white">Game Library</h1>
      
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search games..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-colors duration-200"
          />
        </div>
        
        <div className="md:w-48">
          <select 
            value={filter} 
            onChange={(e) => setFilter(e.target.value)}
            className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-red-400 transition-colors duration-200"
          >
            <option value="all">All Genres</option>
            <option value="action">Action</option>
            <option value="adventure">Adventure</option>
            <option value="rpg">RPG</option>
            <option value="simulation">Simulation</option>
            <option value="strategy">Strategy</option>
            <option value="puzzle">Puzzle</option>
          </select>
        </div>
      </div>
      
      {filteredGames.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredGames.map(game => (
            <GameCard key={game.id} game={game} />
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <FaGamepad className="text-6xl text-gray-600 mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">No games found</h3>
          <p className="text-gray-400 mb-6">
            {searchTerm || filter !== 'all' ? 
              'Try changing your search or filter' : 
              'Your game library is empty'}
          </p>
          <Link 
            to="/browse" 
            className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 no-underline"
          >
            Browse Games
          </Link>
        </div>
      )}
    </section>
  );
};

LibrarySection.propTypes = {
  games: PropTypes.array.isRequired
};

// Settings Section Component
const SettingsSection = ({ user }) => {
  const [profileData, setProfileData] = useState({
    username: user?.username || '',
    email: user?.email || '',
    bio: user?.bio || ''
  });
  
  // Add avatar state
  const [avatar, setAvatar] = useState({
    file: null,
    preview: user?.profileImage || null,
    uploading: false,
    error: ''
  });

  // Reference to the file input element
  const fileInputRef = useRef(null);
  
  // Update profile data when user data changes
  useEffect(() => {
    setProfileData({
      username: user?.username || '',
      email: user?.email || '',
      bio: user?.bio || ''
    });
    
    // Set initial avatar preview if user has a profile image
    if (user?.profileImage) {
      setAvatar(prev => ({
        ...prev,
        preview: user.profileImage
      }));
    }
  }, [user]); // Re-run this effect when user data changes

  // Add file cleanup on unmount
  useEffect(() => {
    return () => {
      // Revoke the object URL to avoid memory leaks
      if (avatar.file && avatar.preview) {
        URL.revokeObjectURL(avatar.preview);
      }
    };
  }, [avatar.file, avatar.preview]);
  


  // Add password change state
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  
  // Add feedback state
  const [feedback, setFeedback] = useState({
    profile: { message: '', type: '' },
    password: { message: '', type: '' },
    avatar: { message: '', type: '' }
  });
  
  const [loading, setLoading] = useState({
    profile: false,
    password: false,
    avatar: false
  });
  
  // Handle profile form input changes
  const handleProfileChange = (e) => {
    setProfileData({
      ...profileData,
      [e.target.id]: e.target.value
    });
  };
  
  // Handle password form input changes
  const handlePasswordChange = (e) => {
    setPasswordData({
      ...passwordData,
      [e.target.id]: e.target.value
    });
  };
  
  // Handle avatar file selection
  const handleAvatarChange = (e) => {
    const selectedFile = e.target.files[0];
    
    if (!selectedFile) {
      return;
    }
    
    // Validate file type
    const validTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!validTypes.includes(selectedFile.type)) {
      setAvatar({
        ...avatar,
        error: 'Invalid file type. Please choose a JPEG, PNG, or GIF image.'
      });
      return;
    }
    
    // Validate file size (1MB max)
    if (selectedFile.size > 1024 * 1024) {
      setAvatar({
        ...avatar,
        error: 'File too large. Maximum file size is 1MB.'
      });
      return;
    }
    
    // Create a preview URL
    const previewUrl = URL.createObjectURL(selectedFile);
    
    setAvatar({
      file: selectedFile,
      preview: previewUrl,
      uploading: false,
      error: ''
    });
    
    // Clear any previous error messages
    setFeedback({
      ...feedback,
      avatar: { message: '', type: '' }
    });
  };
  
  // Trigger file input click
  const triggerFileInput = () => {
    fileInputRef.current.click();
  };
  
  // Handle avatar upload
  const handleAvatarUpload = async (e) => {
    e.preventDefault();
    
    if (!avatar.file) {
      setFeedback({
        ...feedback,
        avatar: { message: 'Please select an image file first', type: 'error' }
      });
      return;
    }
    
    setLoading({ ...loading, avatar: true });
    
    try {
      // Create form data for file upload
      const formData = new FormData();
      formData.append('avatar', avatar.file);
      
      const response = await fetch('/api/users/avatar', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setFeedback({
          ...feedback,
          avatar: { message: 'Avatar updated successfully', type: 'success' }
        });
        
        // Update auth context with new avatar (may require a refresh of user data)
        // Ideally your auth context would provide an updateUser method
      } else {
        setFeedback({
          ...feedback,
          avatar: { message: data.message || 'Failed to update avatar', type: 'error' }
        });
      }
    } catch (error) {
      console.error('Avatar upload error:', error);
      setFeedback({
        ...feedback,
        avatar: { message: 'Error uploading avatar', type: 'error' }
      });
    } finally {
      setLoading({ ...loading, avatar: false });
    }
  };
  
  // Submit profile update
  const handleProfileSubmit = async (e) => {
    e.preventDefault();
    setLoading({...loading, profile: true});
    
    // Username validation
    if (profileData.username.trim().length < 3) {
      setFeedback({
        ...feedback,
        profile: {
          message: 'Username must be at least 3 characters long',
          type: 'error'
        }
      });
      setLoading({...loading, profile: false});
      return;
    }
    
    try {

      
      // Send request to update profile
      const response = await fetch('/api/users/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          username: profileData.username,
          bio: profileData.bio
        })
      });
      
      // Get response text first to see what's being returned
      const responseText = await response.text();
      
      // Try to parse as JSON if there's content
      let data;
      try {
        data = responseText ? JSON.parse(responseText) : {};
      } catch (parseError) {
        console.error('Failed to parse response as JSON:', parseError);
        throw new Error('Server returned an invalid response');
      }
      
      if (response.ok) {
        // Update successful
        setFeedback({
          ...feedback,
          profile: {
            message: 'Profile updated successfully',
            type: 'success'
          }
        });
      } else {
        // Error occurred
        console.error('Profile update failed:', data);
        setFeedback({
          ...feedback,
          profile: {
            message: data.message || 'Failed to update profile',
            type: 'error'
          }
        });
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      setFeedback({
        ...feedback,
        profile: {
          message: 'An error occurred while updating profile: ' + error.message,
          type: 'error'
        }
      });
    } finally {
      setLoading({...loading, profile: false});
    }
  };
  
  // Submit password update
  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    setLoading({...loading, password: true});
    
    // Validate passwords
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setFeedback({
        ...feedback,
        password: { 
          message: 'New passwords do not match', 
          type: 'error' 
        }
      });
      setLoading({...loading, password: false});
      return;
    }
    
    if (passwordData.newPassword.length < 8) {
      setFeedback({
        ...feedback,
        password: { 
          message: 'Password must be at least 8 characters long', 
          type: 'error' 
        }
      });
      setLoading({...loading, password: false});
      return;
    }
    
    try {
      // Send request to backend
      const response = await fetch('/api/auth/change-password', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword
        })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        // Success
        setFeedback({
          ...feedback,
          password: { 
            message: 'Password updated successfully', 
            type: 'success' 
          }
        });
        
        // Clear password fields
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        });
      } else {
        // Error
        setFeedback({
          ...feedback,
          password: { 
            message: data.message || 'Failed to update password', 
            type: 'error' 
          }
        });
      }
    } catch {
      setFeedback({
        ...feedback,
        password: { 
          message: 'An error occurred. Please try again.', 
          type: 'error' 
        }
      });
    } finally {
      setLoading({...loading, password: false});
    }
  };
  
  // Get the appropriate avatar display
  const getAvatarDisplay = () => {
    if (avatar.preview) {
      return <img src={getSecureImageUrl(avatar.preview)} alt={user?.username} className="w-full h-full object-cover rounded-full" />;
    } else {
      return <FaUser className="text-4xl text-gray-400" />;
    }
  };
  
  return (
    <section className="space-y-8">
      <h1 className="text-3xl font-bold text-white">Account Settings</h1>
      
      {/* Avatar Section */}
      <form className="space-y-6" onSubmit={handleAvatarUpload}>
        <div className="bg-gray-700/30 p-6 rounded-lg border border-gray-600">
          <h2 className="text-xl font-semibold text-white mb-6 pb-3 border-b border-gray-600">Profile Picture</h2>
          
          <div className="flex flex-col md:flex-row items-center gap-6">
            <div className="w-24 h-24 bg-gray-600 rounded-full flex items-center justify-center overflow-hidden border-2 border-gray-500">
              {getAvatarDisplay()}
            </div>
            <div className="flex-1 space-y-4">
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleAvatarChange}
                accept="image/jpeg,image/png,image/gif"
                className="hidden"
              />
              <button 
                type="button" 
                className="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200"
                onClick={triggerFileInput}
              >
                {avatar.file ? 'Change Selected Image' : 'Select Image'}
              </button>
              {avatar.file && (
                <button 
                  type="submit" 
                  className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={loading.avatar}
                >
                  {loading.avatar ? 'Uploading...' : 'Upload Image'}
                </button>
              )}
              <p className="text-gray-400 text-sm">JPEG or PNG, max 1MB</p>
            </div>
          </div>
          
          {avatar.error && (
            <div className="mt-4 p-3 bg-red-500/20 border border-red-500/50 rounded-lg text-red-300">
              {avatar.error}
            </div>
          )}
          
          {feedback.avatar.message && (
            <div className={`mt-4 p-3 rounded-lg ${
              feedback.avatar.type === 'success' 
                ? 'bg-green-500/20 border border-green-500/50 text-green-300' 
                : 'bg-red-500/20 border border-red-500/50 text-red-300'
            }`}>
              {feedback.avatar.message}
            </div>
          )}
        </div>
      </form>
      
      {/* Profile Information Form */}
      <form className="space-y-6" onSubmit={handleProfileSubmit}>
        <div className="bg-gray-700/30 p-6 rounded-lg border border-gray-600">
          <h2 className="text-xl font-semibold text-white mb-6 pb-3 border-b border-gray-600">Profile Information</h2>
          
          <div className="space-y-4">
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-2">Username</label>
              <input 
                type="text" 
                id="username" 
                value={profileData.username} 
                onChange={handleProfileChange}
                minLength={3}
                required
                className="w-full p-3 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-colors duration-200"
              />
            </div>
            
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
              <input 
                type="email" 
                id="email" 
                value={profileData.email} 
                readOnly
                className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-gray-400 cursor-not-allowed"
                title="Email address cannot be changed"
              />
              <small className="text-gray-500 text-xs mt-1 block">Email address cannot be changed</small>
            </div>
            
            <div>
              <label htmlFor="bio" className="block text-sm font-medium text-gray-300 mb-2">Bio</label>
              <textarea 
                id="bio" 
                rows="4"
                placeholder="Tell others about yourself..."
                value={profileData.bio}
                onChange={handleProfileChange}
                className="w-full p-3 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-colors duration-200 resize-none"
              ></textarea>
            </div>
          </div>
          
          {feedback.profile.message && (
            <div className={`mt-4 p-3 rounded-lg ${
              feedback.profile.type === 'success' 
                ? 'bg-green-500/20 border border-green-500/50 text-green-300' 
                : 'bg-red-500/20 border border-red-500/50 text-red-300'
            }`}>
              {feedback.profile.message}
            </div>
          )}
          
          <div className="mt-6">
            <button 
              type="submit" 
              className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={loading.profile}
            >
              {loading.profile ? 'Saving...' : 'Save Profile'}
            </button>
          </div>
        </div>
      </form>
      
      {/* Password Form */}
      <form className="space-y-6" onSubmit={handlePasswordSubmit}>
        <div className="bg-gray-700/30 p-6 rounded-lg border border-gray-600">
          <h2 className="text-xl font-semibold text-white mb-6 pb-3 border-b border-gray-600">Password</h2>
          
          <div className="space-y-4">
            <div>
              <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-300 mb-2">Current Password</label>
              <input 
                type="password" 
                id="currentPassword" 
                value={passwordData.currentPassword}
                onChange={handlePasswordChange}
                required
                className="w-full p-3 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-colors duration-200"
              />
            </div>
            
            <div>
              <label htmlFor="newPassword" className="block text-sm font-medium text-gray-300 mb-2">New Password</label>
              <input 
                type="password" 
                id="newPassword" 
                value={passwordData.newPassword}
                onChange={handlePasswordChange}
                required
                minLength={8}
                className="w-full p-3 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-colors duration-200"
              />
            </div>
            
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300 mb-2">Confirm New Password</label>
              <input 
                type="password" 
                id="confirmPassword" 
                value={passwordData.confirmPassword}
                onChange={handlePasswordChange}
                required
                className="w-full p-3 bg-gray-600 border border-gray-500 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-red-400 transition-colors duration-200"
              />
            </div>
          </div>
          
          {feedback.password.message && (
            <div className={`mt-4 p-3 rounded-lg ${
              feedback.password.type === 'success' 
                ? 'bg-green-500/20 border border-green-500/50 text-green-300' 
                : 'bg-red-500/20 border border-red-500/50 text-red-300'
            }`}>
              {feedback.password.message}
            </div>
          )}
          
          <div className="mt-6">
            <button 
              type="submit" 
              className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={loading.password}
            >
              {loading.password ? 'Updating...' : 'Update Password'}
            </button>
          </div>
        </div>
      </form>
      
      <div className="bg-red-500/10 border border-red-500/30 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-red-400 mb-4">Danger Zone</h3>
        <button className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 mb-3">
          Delete Account
        </button>
        <p className="text-gray-400 text-sm">This action cannot be undone. All your data will be permanently deleted.</p>
      </div>
    </section>
  );
};

SettingsSection.propTypes = {
  user: PropTypes.object.isRequired
};

// Wishlist Section Component
const WishlistSection = ({ wishlist }) => {
  return (
    <section className="space-y-6">
      <h1 className="text-3xl font-bold text-white">Wishlist</h1>
      
      {wishlist.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {wishlist.map(game => (
            <div key={game.id} className="space-y-4">
              <GameCard game={game} />
              <div className="flex gap-2">
                <button className="flex-1 bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                  Remove
                </button>
                <Link 
                  to={`/game/${game.id}`} 
                  className="flex-1 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 text-center no-underline"
                >
                  {game.paymentType === 'free' ? 'Get Free' : 'Buy Now'}
                </Link>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <FaHeart className="text-6xl text-gray-600 mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">Your wishlist is empty</h3>
          <p className="text-gray-400 mb-6">Save games you&apos;re interested in to your wishlist</p>
          <Link 
            to="/browse" 
            className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 no-underline"
          >
            Browse Games
          </Link>
        </div>
      )}
    </section>
  );
};

WishlistSection.propTypes = {
  wishlist: PropTypes.array.isRequired
};

// Purchases Section Component
const PurchasesSection = () => {
  const purchases = [
    { id: 1, gameName: "Space Explorer", date: "2023-10-15", amount: "$9.99", status: "completed" },
    { id: 2, gameName: "Fantasy Quest", date: "2023-09-22", amount: "150 Credits", status: "completed" },
    { id: 3, gameName: "Credits Bundle 500", date: "2023-09-05", amount: "$4.99", status: "completed" }
  ];
  
  return (
    <section className="space-y-6">
      <h1 className="text-3xl font-bold text-white">Purchase History</h1>
      
      {purchases.length > 0 ? (
        <div className="bg-gray-700/30 rounded-lg border border-gray-600 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-600">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-200">Date</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-200">Item</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-200">Amount</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-200">Status</th>
                  <th className="px-6 py-4 text-left text-sm font-semibold text-gray-200">Action</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-600">
                {purchases.map(purchase => (
                  <tr key={purchase.id} className="hover:bg-gray-700/50 transition-colors duration-200">
                    <td className="px-6 py-4 text-sm text-gray-300">{formatDate(purchase.date)}</td>
                    <td className="px-6 py-4 text-sm text-white font-medium">{purchase.gameName}</td>
                    <td className="px-6 py-4 text-sm text-gray-300">{purchase.amount}</td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                        purchase.status === 'completed' 
                          ? 'bg-green-500/20 text-green-300' 
                          : 'bg-yellow-500/20 text-yellow-300'
                      }`}>
                        {purchase.status}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <button className="bg-gray-600 hover:bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium transition-colors duration-200">
                        Receipt
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <FaHistory className="text-6xl text-gray-600 mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">No purchase history</h3>
          <p className="text-gray-400">Your purchases will appear here</p>
        </div>
      )}
    </section>
  );
};

// Uploads Section Component
const UploadsSection = () => {
  const [myUploads, setMyUploads] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [analytics, setAnalytics] = useState({
    totalDownloads: 0,
    totalRevenue: 0,
    averageRating: 0
  });

  // Fetch user's uploaded games when component mounts
  useEffect(() => {
    const fetchUserUploads = async () => {
      try {
        setLoading(true);
        // Use the absolute URL to the backend API
        const response = await fetch(`${API_URL}/games/my-uploads`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to fetch your uploaded games');
        }

        const data = await response.json();
        
        // Transform API data to match the component's expected format
        const formattedUploads = data.games.map(game => ({
          id: game.id,
          title: game.title,
          image: getSecureImageUrl(game.coverImage || `https://via.placeholder.com/300x150?text=${encodeURIComponent(game.title)}`),
          downloads: game.downloadCount || 0,
          rating: game.averageRating || 0,
          status: game.status || 'pending',
          date: game.createdAt,
          revenue: game.revenue || (game.priceModel === 'free' ? '$0.00' : 'N/A')
        }));

        setMyUploads(formattedUploads);
        
        // Calculate analytics totals
        if (data.games.length > 0) {
          const totalDownloads = data.games.reduce((sum, game) => sum + (game.downloadCount || 0), 0);
          const totalRevenue = data.totalRevenue || 0;
          const ratings = data.games.filter(game => game.averageRating > 0).map(game => game.averageRating);
          const averageRating = ratings.length > 0 
            ? (ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length).toFixed(1) 
            : 0;

          setAnalytics({
            totalDownloads,
            totalRevenue,
            averageRating
          });
        }
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching user uploads:', err);
        setError(err.message);
        setLoading(false);
      }
    };

    fetchUserUploads();
  }, []);

  if (loading) {
    return (
      <div className="loading-spinner">
        <div className="spinner"></div>
        <p>Loading your uploads...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-message">
        <h3>Error loading uploads</h3>
        <p>{error}</p>
        <button onClick={() => window.location.reload()} className="btn">
          Try Again
        </button>
      </div>
    );
  }
  
  return (
    <section className="space-y-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <h1 className="text-3xl font-bold text-white">My Uploaded Games</h1>
        <Link 
          to="/upload-game" 
          className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2 no-underline w-fit"
        >
          <FaUpload /> Upload New Game
        </Link>
      </div>
      
      {myUploads.length > 0 ? (
        <div className="space-y-6">
          {myUploads.map(game => (
            <div key={game.id} className="bg-gray-700/30 p-6 rounded-lg border border-gray-600 flex flex-col lg:flex-row gap-6">
              <div className="relative lg:w-48 lg:h-32">
                <img 
                  src={getSecureImageUrl(game.image)} 
                  alt={game.title} 
                  className="w-full h-32 lg:h-full object-cover rounded-lg"
                  onError={(e) => {
                    e.target.onerror = null; 
                    e.target.src = gamePlaceholder;
                  }}
                />
                <span className={`absolute top-2 right-2 px-2 py-1 text-xs font-medium rounded-full ${
                  game.status === 'approved' 
                    ? 'bg-green-500/20 text-green-300' 
                    : 'bg-yellow-500/20 text-yellow-300'
                }`}>
                  {game.status === 'approved' ? 'Live' : 'Pending Review'}
                </span>
              </div>
              
              <div className="flex-1 space-y-4">
                <h3 className="text-xl font-semibold text-white">{game.title}</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <span className="block text-sm text-gray-400">Downloads</span>
                    <span className="block text-lg font-semibold text-white">{game.downloads}</span>
                  </div>
                  
                  <div className="text-center">
                    <span className="block text-sm text-gray-400">Rating</span>
                    <span className="block text-lg font-semibold text-white">
                      {game.rating != null && !isNaN(Number(game.rating)) 
                        ? Number(game.rating).toFixed(1) 
                        : 'No ratings'} ★
                    </span>
                  </div>
                  
                  <div className="text-center">
                    <span className="block text-sm text-gray-400">Uploaded</span>
                    <span className="block text-lg font-semibold text-white">{formatDate(game.date)}</span>
                  </div>
                  
                  <div className="text-center">
                    <span className="block text-sm text-gray-400">Revenue</span>
                    <span className="block text-lg font-semibold text-white">{game.revenue}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex flex-col gap-2 lg:w-32">
                <Link 
                  to={`/edit-game/${game.id}`} 
                  className="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2 no-underline"
                >
                  <FaEdit /> Edit
                </Link>
                <Link 
                  to={`/game/${game.id}/dashboard`} 
                  className="border border-gray-500 hover:border-gray-400 text-gray-300 hover:text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 text-center no-underline"
                >
                  Dashboard
                </Link>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-16 text-center">
          <FaShoppingCart className="text-6xl text-gray-600 mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">No uploaded games</h3>
          <p className="text-gray-400 mb-6">Share your games with the community</p>
          <Link 
            to="/upload-game" 
            className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 no-underline"
          >
            Upload Game
          </Link>
        </div>
      )}
      
      {myUploads.length > 0 && (
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-white">Your Analytics Summary</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-700/30 p-6 rounded-lg border border-gray-600">
              <h3 className="text-lg font-semibold text-gray-300 mb-2">Total Downloads</h3>
              <p className="text-3xl font-bold text-white mb-2">{analytics.totalDownloads}</p>
              <span className="text-gray-500 text-sm">--</span>
            </div>
            
            <div className="bg-gray-700/30 p-6 rounded-lg border border-gray-600">
              <h3 className="text-lg font-semibold text-gray-300 mb-2">Total Revenue</h3>
              <p className="text-3xl font-bold text-white mb-2">${analytics.totalRevenue.toFixed(2)}</p>
              <span className="text-gray-500 text-sm">--</span>
            </div>
            
            <div className="bg-gray-700/30 p-6 rounded-lg border border-gray-600">
              <h3 className="text-lg font-semibold text-gray-300 mb-2">Average Rating</h3>
              <p className="text-3xl font-bold text-white mb-2">{analytics.averageRating}</p>
              <span className="text-gray-500 text-sm">--</span>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

// Helper function to format dates
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

// Sample data for testing
const sampleGames = [
  {
    id: 1,
    title: "Space Explorer",
    image: "https://via.placeholder.com/300x150?text=Space+Explorer",
    genre: "adventure",
    paymentType: "paid",
    price: "$9.99",
    tags: ["space", "exploration", "sci-fi"]
  },
  {
    id: 2,
    title: "Fantasy Quest",
    image: "https://via.placeholder.com/300x150?text=Fantasy+Quest",
    genre: "rpg",
    paymentType: "credits",
    price: "150 Credits",
    tags: ["fantasy", "rpg", "adventure"]
  },
  {
    id: 3,
    title: "Puzzle Master",
    image: "https://via.placeholder.com/300x150?text=Puzzle+Master",
    genre: "puzzle",
    paymentType: "free",
    price: "Free",
    tags: ["puzzle", "strategy"]
  },
  {
    id: 4,
    title: "Zombie Survival",
    image: "https://via.placeholder.com/300x150?text=Zombie+Survival",
    genre: "action",
    paymentType: "paid",
    price: "$4.99",
    tags: ["zombies", "survival", "action"]
  }
];

const sampleWishlist = [
  {
    id: 5,
    title: "Racing Champions",
    image: "https://via.placeholder.com/300x150?text=Racing+Champions",
    genre: "sports",
    paymentType: "paid",
    price: "$12.99",
    tags: ["racing", "sports", "multiplayer"]
  },
  {
    id: 6,
    title: "Medieval Kingdom",
    image: "https://via.placeholder.com/300x150?text=Medieval+Kingdom",
    genre: "strategy",
    paymentType: "credits",
    price: "200 Credits",
    tags: ["strategy", "medieval", "building"]
  },
  {
    id: 7,
    title: "Pixel Platformer",
    image: "https://via.placeholder.com/300x150?text=Pixel+Platformer",
    genre: "platformer",
    paymentType: "free",
    price: "Free",
    tags: ["pixel-art", "platformer", "indie"]
  }
];

export default ProfilePage;