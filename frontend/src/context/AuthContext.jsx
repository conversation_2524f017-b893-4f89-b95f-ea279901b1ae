import { createContext, useState, useContext, useEffect } from 'react';
import PropTypes from 'prop-types';
import api from '../services/api';
// Remove the problematic import
// import { checkAuthStatus } from '../services/authService';
// Import logCookieDetails if it exists, otherwise leave it out
// import { logCookieDetails } from '../utils/debugHelper';

// Create context
const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Check if user is authenticated on load
  useEffect(() => {
    const verifyAuthStatus = async () => {
  
      
      try {
        // Call the me endpoint directly instead of using imported function
        const response = await api.get('/auth/me');
        
        if (response.data) {

          setUser(response.data);
        } else {
          
          setUser(null);
        }
      } catch (err) {
        console.error('❌ Auth status error:', err.message);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    // Log cookie details for debugging if the function exists
    if (typeof window !== 'undefined') {
      try {
        // Check if window.debugCookies exists before using it
        window.debugCookies?.logCookieDetails && window.debugCookies.logCookieDetails();
      } catch (e) {
        console.error('Error logging cookies:', e);
      }
    }

    verifyAuthStatus();
  }, []);

  // Login function - updated to handle social login
  const login = async (credentials, isSocialLogin = false, skipApiCall = false) => {
    try {
      setLoading(true);
      setError(null);
      
      // If this is a social login with skipApiCall flag, 
      // just set the user directly without making an API call
      if (isSocialLogin && skipApiCall && typeof credentials === 'object') {
  
        setUser(credentials);
        return credentials;
      }
      

      
      const response = await api.post('/auth/login', credentials);
      
      if (response.data && response.data.user) {
        setUser(response.data.user);
        
        return response.data.user;
      } else {
        throw new Error('No user data in response');
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Login failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      await api.post('/auth/logout', {});
      
      setUser(null);
    } catch (err) {
      console.error('❌ Logout error:', err.message);
      setUser(null);
    }
  };

  // Register function
  const register = async (userData) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.post(`/auth/register`, userData);
      
      if (response.data && response.data.user) {
        setUser(response.data.user);
        return response.data.user;
      } else {
        throw new Error('Registration failed - no user data');
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Registration failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update user function
  const updateUser = async (userData) => {
    if (!user) return null;
    
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.put(`/users/${user.id}`, userData);
      
      if (response.data) {
        setUser(response.data);
        return response.data;
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Update failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      loading,
      error,
      login,
      logout,
      register,
      updateUser
    }}>
      {children}
    </AuthContext.Provider>
  );
};

AuthProvider.propTypes = {
  children: PropTypes.node.isRequired
};

// Hook for using the auth context
export const useAuth = () => useContext(AuthContext);

export default AuthContext;
