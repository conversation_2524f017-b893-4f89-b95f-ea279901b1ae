const express = require('express');
const router = express.Router();

// Import user controller
const userController = require('../controllers/user.controller');

// Auth routes
router.post('/register', userController.register);
router.post('/login', userController.login);
router.post('/logout', userController.logout);

// User profile routes
router.get('/profile', userController.getProfile);
router.put('/profile', userController.updateProfile);

// Public user profile routes
router.get('/:userId/profile', userController.getPublicProfile);
router.get('/:userId/reviews', userController.getUserReviews);
router.get('/:userId/posts', userController.getUserPosts);

module.exports = router;