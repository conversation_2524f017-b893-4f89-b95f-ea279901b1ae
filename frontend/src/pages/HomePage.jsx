import { useState, useEffect } from 'react';
import GameCard from '../components/GameCard';
import Sidebar from '../components/Sidebar';
import axios from 'axios';
import { API_URL } from '../config/env.js';
import { useLanguage } from '../context/LanguageContext';
import { useSidebar } from '../context/SidebarContext';

const HomePage = () => {
  const { t } = useLanguage();
  const { isSidebarOpen, toggleSidebar, isInitialized } = useSidebar();

  // Add state for actual games data
  const [gamesData, setGamesData] = useState({
    freeGames: [],
    topGames: [],
    newReleases: []
  });

  // Add loading and error states
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // State to track visible cards count based on screen width
  const [visibleCardCounts, setVisibleCardCounts] = useState({
    freeGames: 6,
    topGames: 6,
    newReleases: 6
  });

  // State for visible games (limited by count)
  const [games, setGames] = useState({
    freeGames: [],
    topGames: [],
    newReleases: []
  });

  // State to track expanded sections
  const [expandedSections, setExpandedSections] = useState({
    freeGames: false,
    topGames: false,
    newReleases: false
  });



  // Fetch games from the API
  useEffect(() => {
    const fetchGames = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await axios.get(`${API_URL}/games`);
        
        // Extract the games array from the response
        const gamesArray = response.data.games || [];
        
        if (!Array.isArray(gamesArray)) {
          console.error('Expected games array not found in response:', response.data);
          throw new Error('Invalid response format from API');
        }
        
        // Transform the database data to match the expected format for GameCard
        const formattedGames = gamesArray.map(game => ({
          id: game.id,
          title: game.title,
          description: game.description,
          image: game.cardImage || game.image, // Use actual uploaded card image first, fallback to cover image
          hoverGif: game.animationGif, // Use actual uploaded GIF animation
          genre: game.genre,
          tags: game.tags ? game.tags.split(',').map(tag => tag.trim()) : [],
          paymentType: game.priceModel,
          price: game.priceModel === 'paid' ? `$${parseFloat(game.price).toFixed(2)}` : 
                 game.priceModel === 'credits' ? `${game.creditPrice} Credits` : 'Free',
          releaseDate: new Date(game.releaseDate)
        }));
        
        // Categorize games
        const freeGames = formattedGames.filter(game => game.paymentType === 'free');
        
        // Sort by rating/popularity for top games (using a random sample for now)
        const topGames = [...formattedGames].sort(() => 0.5 - Math.random());
        
        // Sort by release date for new releases
        const newReleases = [...formattedGames].sort(
          (a, b) => new Date(b.releaseDate) - new Date(a.releaseDate)
        );
        
        // Set all categorized games
        setGamesData({
          freeGames,
          topGames,
          newReleases
        });
        
        // Initialize visible games based on counts
        updateVisibleGames({
          freeGames,
          topGames,
          newReleases
        });
        
      } catch (err) {
        console.error('Error fetching games:', err);
        setError(t('homepage.loading.error'));
      } finally {
        setLoading(false);
      }
    };
    
    fetchGames();
  }, []);

  // Update card count based on screen size
  useEffect(() => {
    const updateCardCount = () => {
      let count = 6; // Default for 1080p
      
      if (window.innerWidth >= 2560) {
        count = 8; // 4K
      } else if (window.innerWidth >= 1920) {
        count = 7; // 2K
      } else if (window.innerWidth < 1536) {
        count = 5;
      } else if (window.innerWidth < 1280) {
        count = 4;
      } else if (window.innerWidth < 960) {
        count = 3;
      } else if (window.innerWidth < 768) {
        count = 2;
      } else if (window.innerWidth < 480) {
        count = 1;
      }
      
      const newCounts = {
        freeGames: count,
        topGames: count,
        newReleases: count
      };
      
      setVisibleCardCounts(newCounts);
      
      // Update visible games based on new counts
      updateVisibleGames(gamesData, newCounts);
    };
    
    // Initial update
    updateCardCount();
    
    // Add resize listener
    window.addEventListener('resize', updateCardCount);
    
    // Clean up
    return () => window.removeEventListener('resize', updateCardCount);
  }, [gamesData]);

  // Helper function to update visible games based on counts and expanded state
  const updateVisibleGames = (data = gamesData, counts = visibleCardCounts) => {
    setGames({
      freeGames: expandedSections.freeGames 
        ? data.freeGames 
        : data.freeGames.slice(0, counts.freeGames),
      topGames: expandedSections.topGames 
        ? data.topGames 
        : data.topGames.slice(0, counts.topGames),
      newReleases: expandedSections.newReleases 
        ? data.newReleases 
        : data.newReleases.slice(0, counts.newReleases)
    });
  };

  // Toggle section expansion
  const toggleSection = (section) => {
    setExpandedSections(prev => {
      const newState = {
        ...prev,
        [section]: !prev[section]
      };
      
      // Update visible games based on the new expanded state
      setGames(prevGames => ({
        ...prevGames,
        [section]: newState[section] 
          ? gamesData[section] 
          : gamesData[section].slice(0, visibleCardCounts[section])
      }));
      
      return newState;
    });
    
    // Scroll to section when expanding
    if (!expandedSections[section]) {
      setTimeout(() => {
        document.querySelector(`.${section}-section`).scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        });
      }, 100);
    }
  };

  // Render loading state
  if (loading) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] p-5 flex items-center justify-center">
        <div className="text-white text-xl">{t('homepage.loading.games')}</div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] p-5 flex items-center justify-center">
        <div className="text-red-500 text-xl">{error}</div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] flex">
      {/* Sidebar */}
      <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />

      {/* Main Content */}
      <div className={`flex-1 ${isInitialized ? 'transition-all duration-300' : ''} ${isSidebarOpen ? 'lg:pl-64' : 'lg:pl-0'} p-5`}>
        {/* Free Games Section */}
        <section className="mb-[60px] overflow-hidden animate-fadeIn relative free-games-section">
        <div className="flex items-center mb-[25px] relative">
          <h2 className="text-[1.8rem] font-extrabold uppercase tracking-[1.5px] text-white m-0 pr-5 relative z-[1] text-transparent bg-clip-text bg-gradient-to-r from-[#00b09b] to-[#96c93d] drop-shadow-[0_0_10px_rgba(255,69,0,0.5)]">
            {t('homepage.sections.freeGames')}
          </h2>
          <div className="h-[3px] flex-grow bg-gradient-to-r from-[rgba(0,176,155,0.8)] via-[rgba(150,201,61,0.3)] to-transparent rounded-[2px] mr-5"></div>
          {!expandedSections.freeGames && gamesData.freeGames.length > visibleCardCounts.freeGames && (
            <button
              onClick={() => toggleSection('freeGames')}
              className="bg-transparent border-2 border-[rgba(255,69,0,0.7)] text-[#ff4500] rounded-[20px] px-4 py-1.5 text-[0.8rem] font-semibold uppercase cursor-pointer transition-all duration-300 flex items-center justify-center whitespace-nowrap tracking-[0.5px] hover:bg-[rgba(255,69,0,0.9)] hover:text-white hover:-translate-y-0.5 hover:shadow-[0_4px_8px_rgba(255,69,0,0.4)] after:content-['→'] after:ml-1.5 after:text-base after:transition-transform after:duration-300"
            >
              {t('homepage.buttons.showMore')}
            </button>
          )}
        </div>
        <div className={`grid gap-3 transition-[max-height] duration-500 ease-in-out
          ${expandedSections.freeGames ? 'grid-rows-[unset] auto-rows-fr max-h-[3000px]' : 'grid-rows-1 grid-auto-rows-0'}
          2xl:grid-cols-7 lg:grid-cols-6 md:grid-cols-4 sm:grid-cols-3 grid-cols-1`}
        >
          {games.freeGames.map((game, index) => (
            <div key={game.id} className="h-full animate-fadeIn" style={{ animationDelay: `${index * 0.1}s` }}>
              <GameCard game={game} />
            </div>
          ))}
        </div>
        {expandedSections.freeGames && (
          <button
            onClick={() => toggleSection('freeGames')}
            className="mx-auto mt-5 bg-[rgba(255,69,0,0.1)] border-2 border-[rgba(255,69,0,0.4)] text-[#ff4500] rounded-[20px] px-4 py-1.5 text-[0.8rem] font-semibold uppercase cursor-pointer transition-all duration-300 flex items-center justify-center whitespace-nowrap tracking-[0.5px] hover:bg-[rgba(255,69,0,0.9)] hover:text-white hover:-translate-y-0.5 hover:shadow-[0_4px_8px_rgba(255,69,0,0.4)] before:content-['↑'] before:mr-1.5 before:text-[0.9rem]"
          >
            {t('homepage.buttons.showLess')}
          </button>
        )}
      </section>

      {/* Top Games Section */}
      <section className="mb-[60px] overflow-hidden animate-fadeIn relative top-games-section">
        <div className="flex items-center mb-[25px] relative">
          <h2 className="text-[1.8rem] font-extrabold uppercase tracking-[1.5px] text-white m-0 pr-5 relative z-[1] text-transparent bg-clip-text bg-gradient-to-r from-[#ff4500] to-[#ff8c00] drop-shadow-[0_0_10px_rgba(255,69,0,0.5)]">
            {t('homepage.sections.topGames')}
          </h2>
          <div className="h-[3px] flex-grow bg-gradient-to-r from-[rgba(255,69,0,0.8)] via-[rgba(255,140,0,0.3)] to-transparent rounded-[2px] mr-5"></div>
          {!expandedSections.topGames && gamesData.topGames.length > visibleCardCounts.topGames && (
            <button
              onClick={() => toggleSection('topGames')}
              className="bg-transparent border-2 border-[rgba(255,69,0,0.7)] text-[#ff4500] rounded-[20px] px-4 py-1.5 text-[0.8rem] font-semibold uppercase cursor-pointer transition-all duration-300 flex items-center justify-center whitespace-nowrap tracking-[0.5px] hover:bg-[rgba(255,69,0,0.9)] hover:text-white hover:-translate-y-0.5 hover:shadow-[0_4px_8px_rgba(255,69,0,0.4)] after:content-['→'] after:ml-1.5 after:text-base after:transition-transform after:duration-300"
            >
              {t('homepage.buttons.showMore')}
            </button>
          )}
        </div>
        <div className={`grid gap-3 transition-[max-height] duration-500 ease-in-out
          ${expandedSections.topGames ? 'grid-rows-[unset] auto-rows-fr max-h-[3000px]' : 'grid-rows-1 grid-auto-rows-0'}
          2xl:grid-cols-7 lg:grid-cols-6 md:grid-cols-4 sm:grid-cols-3 grid-cols-1`}
        >
          {games.topGames.map((game, index) => (
            <div key={game.id} className="h-full animate-fadeIn" style={{ animationDelay: `${index * 0.1}s` }}>
              <GameCard game={game} />
            </div>
          ))}
        </div>
        {expandedSections.topGames && (
          <button
            onClick={() => toggleSection('topGames')}
            className="mx-auto mt-5 block bg-[rgba(255,69,0,0.1)] border-2 border-[rgba(255,69,0,0.4)] text-[#ff4500] rounded-[20px] px-4 py-1.5 text-[0.8rem] font-semibold uppercase cursor-pointer transition-all duration-300 flex items-center justify-center whitespace-nowrap tracking-[0.5px] hover:bg-[rgba(255,69,0,0.9)] hover:text-white hover:-translate-y-0.5 hover:shadow-[0_4px_8px_rgba(255,69,0,0.4)] before:content-['↑'] before:mr-1.5 before:text-[0.9rem]"
          >
            {t('homepage.buttons.showLess')}
          </button>
        )}
      </section>

      {/* New Releases Section */}
      <section className="mb-[60px] overflow-hidden animate-fadeIn relative new-releases-section">
        <div className="flex items-center mb-[25px] relative">
          <h2 className="text-[1.8rem] font-extrabold uppercase tracking-[1.5px] text-white m-0 pr-5 relative z-[1] text-transparent bg-clip-text bg-gradient-to-r from-[#4a65ff] to-[#5e3bff] drop-shadow-[0_0_10px_rgba(255,69,0,0.5)]">
            {t('homepage.sections.newReleases')}
          </h2>
          <div className="h-[3px] flex-grow bg-gradient-to-r from-[rgba(74,101,255,0.8)] via-[rgba(94,59,255,0.3)] to-transparent rounded-[2px] mr-5"></div>
          {!expandedSections.newReleases && gamesData.newReleases.length > visibleCardCounts.newReleases && (
            <button
              onClick={() => toggleSection('newReleases')}
              className="bg-transparent border-2 border-[rgba(255,69,0,0.7)] text-[#ff4500] rounded-[20px] px-4 py-1.5 text-[0.8rem] font-semibold uppercase cursor-pointer transition-all duration-300 flex items-center justify-center whitespace-nowrap tracking-[0.5px] hover:bg-[rgba(255,69,0,0.9)] hover:text-white hover:-translate-y-0.5 hover:shadow-[0_4px_8px_rgba(255,69,0,0.4)] after:content-['→'] after:ml-1.5 after:text-base after:transition-transform after:duration-300"
            >
              {t('homepage.buttons.showMore')}
            </button>
          )}
        </div>
        <div className={`grid gap-3 transition-[max-height] duration-500 ease-in-out
          ${expandedSections.newReleases ? 'grid-rows-[unset] auto-rows-fr max-h-[3000px]' : 'grid-rows-1 grid-auto-rows-0'}
          2xl:grid-cols-7 lg:grid-cols-6 md:grid-cols-4 sm:grid-cols-3 grid-cols-1`}
        >
          {games.newReleases.map((game, index) => (
            <div key={game.id} className="h-full animate-fadeIn" style={{ animationDelay: `${index * 0.1}s` }}>
              <GameCard game={game} />
            </div>
          ))}
        </div>
        {expandedSections.newReleases && (
          <button
            onClick={() => toggleSection('newReleases')}
            className="mx-auto mt-5 block bg-[rgba(255,69,0,0.1)] border-2 border-[rgba(255,69,0,0.4)] text-[#ff4500] rounded-[20px] px-4 py-1.5 text-[0.8rem] font-semibold uppercase cursor-pointer transition-all duration-300 flex items-center justify-center whitespace-nowrap tracking-[0.5px] hover:bg-[rgba(255,69,0,0.9)] hover:text-white hover:-translate-y-0.5 hover:shadow-[0_4px_8px_rgba(255,69,0,0.4)] before:content-['↑'] before:mr-1.5 before:text-[0.9rem]"
          >
            {t('homepage.buttons.showLess')}
          </button>
        )}
      </section>
      </div>
    </div>
  );
};

export default HomePage;
