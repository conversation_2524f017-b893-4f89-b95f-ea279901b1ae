import { useState, useEffect, useCallback } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import GameCard from '../components/GameCard';
import Sidebar from '../components/Sidebar';
import { FaFilter, FaSortAmountDown, FaChevronDown, FaChevronUp } from 'react-icons/fa';
import axios from 'axios';
import { API_URL } from '../config/env.js';
import { useLanguage } from '../context/LanguageContext';
import { useSidebar } from '../context/SidebarContext';

const CategoryPage = () => {
  const { t } = useLanguage();
  const { category } = useParams();
  const location = useLocation();
  const { isSidebarOpen, toggleSidebar } = useSidebar();

  // Determine if this is a category route or navigation route
  const isCategoryRoute = location.pathname.startsWith('/category/');
  const isNavigationRoute = !isCategoryRoute;
  
  // Games data
  const [games, setGames] = useState([]);
  const [filteredGames, setFilteredGames] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Filter states
  const [filters, setFilters] = useState({
    priceTypes: ['free', 'paid', 'credits'],
    priceRange: { min: 0, max: 100 }
  });
  
  // Sorting
  const [sortOption, setSortOption] = useState('newest');
  const sortOptions = {
    newest: 'Newest',
    oldest: 'Oldest',
    popular: 'Most Popular',
    downloads: 'Most Downloaded',
    alphabetical: 'Name (A-Z)',
    priceAsc: 'Price (Low to High)',
    priceDesc: 'Price (High to Low)'
  };
  
  // Filter visibility
  const [showFilters, setShowFilters] = useState(false);
  const [expandedFilters, setExpandedFilters] = useState({
    price: true
  });



  const fetchGames = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      let apiUrl = `${API_URL}/games`;
      const params = new URLSearchParams();

      // Set a higher limit to get more games
      params.append('limit', '50');

      // Add current sort option (unless it's a navigation route that sets its own sort)
      const isNavigationRoute = !location.pathname.startsWith('/category/');
      if (!isNavigationRoute || (isNavigationRoute && sortOption !== 'newest')) {
        params.append('sort', sortOption);
      }

      // Determine filter type based on route
      if (isCategoryRoute) {
        // For category routes, we need the category parameter
        if (!category) {
          setLoading(false);
          setError('Category not found');
          return; // Exit early if category is not available
        }

        // Category-based filtering (by genre)
        // Map URL category to actual genre names
        const categoryToGenreMap = {
          'action': 'Action',
          'adventure': 'Adventure',
          'basketball': 'Sports',
          'beauty': 'Casual',
          'bike': 'Racing',
          'car': 'Racing',
          'card': 'Card',
          'casual': 'Casual',
          'clicker': 'Casual',
          'controller': 'Action',
          'dress-up': 'Casual',
          'driving': 'Racing',
          'escape': 'Puzzle',
          'flash': 'Arcade',
          'fps': 'Shooter',
          'horror': 'Horror',
          '2-player': 'Multiplayer'
        };

        const genreName = categoryToGenreMap[category] || category;
        if (category === '2-player') {
          params.append('tag', 'multiplayer');
        } else {
          params.append('genre', genreName);
        }
      } else if (isNavigationRoute) {
        // Navigation-based filtering (by special criteria)
        switch (location.pathname) {
          case '/new':
            params.append('sort', 'newest');
            break;
          case '/trending':
            params.append('sort', 'popular');
            break;
          case '/updated':
            // For updated, we'll sort by newest but could add a specific updated_at field later
            params.append('sort', 'newest');
            break;
          case '/multiplayer':
            params.append('tag', 'multiplayer');
            break;
          case '/originals':
            params.append('tag', 'original');
            break;
          case '/recently-played':
            // This would require user authentication and play history
            // For now, show newest games
            params.append('sort', 'newest');
            break;
          default:
            break;
        }
      }

      if (params.toString()) {
        apiUrl += `?${params.toString()}`;
      }

      const response = await axios.get(apiUrl);
      const gamesArray = response.data.games || [];
      
      // Transform the database data to match the expected format for GameCard
      const formattedGames = gamesArray.map(game => ({
        id: game.id,
        title: game.title,
        description: game.description,
        image: game.cardImage || game.image,
        hoverGif: game.animationGif,
        genre: game.genre,
        tags: game.tags ? game.tags.split(',').map(tag => tag.trim()) : [],
        paymentType: game.priceModel,
        price: game.priceModel === 'paid' ? `$${parseFloat(game.price).toFixed(2)}` : 
               game.priceModel === 'credits' ? `${game.creditPrice} Credits` : 'Free',
        releaseDate: new Date(game.releaseDate)
      }));
      
      setGames(formattedGames);
    } catch (err) {
      console.error('Error fetching games:', err);
      setError('Failed to load games. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [category, location.pathname, sortOption, isCategoryRoute]);

  // Fetch games based on category and sort option
  useEffect(() => {
    // For category routes, only fetch if we have the category parameter
    // For navigation routes, we can fetch immediately
    if (isCategoryRoute && !category) {
      setLoading(false);
      setError('Category not found');
      return;
    }

    fetchGames();
  }, [category, location.pathname, sortOption, fetchGames, isCategoryRoute]);

  // Apply filters and sorting
  useEffect(() => {
    if (games.length === 0) return;

    let results = [...games];

    // Apply price type filter
    if (filters.priceTypes.length > 0 && filters.priceTypes.length < 3) {
      results = results.filter(game => 
        filters.priceTypes.includes(game.paymentType)
      );
    }

    // Apply price range filter
    results = results.filter(game => {
      if (game.paymentType === 'free') return true;
      if (game.paymentType === 'paid') {
        const price = parseFloat(game.price.replace(/[^0-9.]/g, ''));
        return price >= filters.priceRange.min && price <= filters.priceRange.max;
      }
      return true;
    });

    // Apply sorting
    results = sortGames(results, sortOption);

    setFilteredGames(results);
  }, [games, filters, sortOption]);

  // Sort games based on selected option (frontend sorting for price filters only)
  const sortGames = (gamesArray, option) => {
    // For backend-supported sorts, the API already returns sorted data
    // Only do frontend sorting for price-based sorts since backend doesn't support them
    switch (option) {
      case 'priceAsc':
        return [...gamesArray].sort((a, b) => {
          const priceA = a.paymentType === 'free' ? 0 : parseFloat(a.price?.replace(/[^0-9.]/g, '') || 0);
          const priceB = b.paymentType === 'free' ? 0 : parseFloat(b.price?.replace(/[^0-9.]/g, '') || 0);
          return priceA - priceB;
        });
      case 'priceDesc':
        return [...gamesArray].sort((a, b) => {
          const priceA = a.paymentType === 'free' ? 0 : parseFloat(a.price?.replace(/[^0-9.]/g, '') || 0);
          const priceB = b.paymentType === 'free' ? 0 : parseFloat(b.price?.replace(/[^0-9.]/g, '') || 0);
          return priceB - priceA;
        });
      default:
        // For backend-supported sorts (newest, oldest, popular, downloads, alphabetical),
        // return as-is since API already sorted them
        return gamesArray;
    }
  };

  // Get page title based on route
  const getPageTitle = () => {
    if (isCategoryRoute) {
      // For category routes, we need the category parameter
      if (!category) {
        return 'Games'; // Fallback title
      }

      // Map URL category to translation key
      const categoryToTranslationMap = {
        '2-player': 'twoPlayer',
        'action': 'action',
        'adventure': 'adventure',
        'basketball': 'basketball',
        'beauty': 'beauty',
        'bike': 'bike',
        'car': 'car',
        'card': 'card',
        'casual': 'casual',
        'clicker': 'clicker',
        'controller': 'controller',
        'dress-up': 'dressUp',
        'driving': 'driving',
        'escape': 'escape',
        'flash': 'flash',
        'fps': 'fps',
        'horror': 'horror'
      };

      const translationKey = categoryToTranslationMap[category];
      if (translationKey) {
        return t(`sidebar.navigation.${translationKey}`);
      }

      // Fallback to formatted category name
      return category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ');
    }

    if (isNavigationRoute) {
      const pathMap = {
        '/new': t('sidebar.navigation.new'),
        '/trending': t('sidebar.navigation.trendingNow'),
        '/updated': t('sidebar.navigation.updated'),
        '/multiplayer': t('sidebar.navigation.multiplayer'),
        '/originals': t('sidebar.navigation.originals'),
        '/recently-played': t('sidebar.navigation.recentlyPlayed')
      };

      return pathMap[location.pathname] || 'Games';
    }

    return 'Games'; // Default fallback
  };

  // Handle filter changes
  const togglePriceTypeFilter = (type) => {
    setFilters(prevFilters => ({
      ...prevFilters,
      priceTypes: prevFilters.priceTypes.includes(type)
        ? prevFilters.priceTypes.filter(t => t !== type)
        : [...prevFilters.priceTypes, type]
    }));
  };

  const handlePriceRangeChange = (key, value) => {
    setFilters(prevFilters => ({
      ...prevFilters,
      priceRange: {
        ...prevFilters.priceRange,
        [key]: value
      }
    }));
  };

  const toggleFilterSection = (section) => {
    setExpandedFilters(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Render loading state
  if (loading) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] flex">
        <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />
        <div className={`flex-1 transition-all duration-300 ${isSidebarOpen ? 'lg:pl-64' : 'lg:pl-0'} p-5 flex items-center justify-center`}>
          <div className="text-white text-xl">Loading games...</div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] flex">
        <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />
        <div className={`flex-1 transition-all duration-300 ${isSidebarOpen ? 'lg:pl-64' : 'lg:pl-0'} p-5 flex items-center justify-center`}>
          <div className="text-red-500 text-xl">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen bg-gradient-to-b from-[#121212] to-[#0a0a0a] flex">
      <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />
      
      <div className={`flex-1 transition-all duration-300 ${isSidebarOpen ? 'lg:pl-64' : 'lg:pl-0'}`}>
        {/* Header */}
        <div className="bg-gray-800 border-b border-gray-700 p-6">
          <div className="max-w-7xl mx-auto">
            <h1 className="text-3xl font-bold text-white mb-4">{getPageTitle()}</h1>
            
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
              <div className="flex items-center gap-2">
                <FaSortAmountDown className="text-gray-400" />
                <select 
                  value={sortOption} 
                  onChange={(e) => setSortOption(e.target.value)}
                  className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-red-500 transition-colors duration-200"
                >
                  {Object.entries(sortOptions).map(([value, label]) => (
                    <option key={value} value={value}>{label}</option>
                  ))}
                </select>
              </div>
              
              <div className="flex items-center gap-4">
                <button 
                  className="sm:hidden flex items-center gap-2 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg font-medium transition-colors duration-200"
                  onClick={() => setShowFilters(!showFilters)}
                >
                  <FaFilter />
                  <span>Filters</span>
                </button>
                
                <div className="text-gray-300 font-medium">
                  {filteredGames.length} games found
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto p-6 flex gap-6">
          {/* Filters Sidebar */}
          <div className={`w-80 bg-gray-800 rounded-lg p-6 border border-gray-700 h-fit sticky top-6 ${
            showFilters 
              ? 'fixed inset-0 z-50 w-full h-full overflow-y-auto sm:relative sm:inset-auto sm:w-80 sm:h-fit sm:z-auto' 
              : 'hidden sm:block'
          }`}>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-white">Filters</h2>
              <button 
                className="sm:hidden text-gray-400 hover:text-red-400 transition-colors duration-200" 
                onClick={() => setShowFilters(false)}
              >
                ×
              </button>
            </div>
            
            {/* Price Filter */}
            <div className="mb-6">
              <div 
                className="flex items-center justify-between cursor-pointer py-2 border-b border-gray-700 mb-4" 
                onClick={() => toggleFilterSection('price')}
              >
                <h3 className="text-lg font-semibold text-white">Price</h3>
                <span className="text-gray-400">
                  {expandedFilters.price ? <FaChevronUp /> : <FaChevronDown />}
                </span>
              </div>
              
              {expandedFilters.price && (
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-2">
                    {[['free', 'Free'], ['paid', 'Paid'], ['credits', 'Credits']].map(([type, label]) => (
                      <label key={type} className="cursor-pointer">
                        <input
                          type="checkbox"
                          checked={filters.priceTypes.includes(type)}
                          onChange={() => togglePriceTypeFilter(type)}
                          className="hidden"
                        />
                        <span className={`block px-3 py-2 rounded-lg text-sm font-medium text-center transition-all duration-200 ${
                          filters.priceTypes.includes(type) 
                            ? 'bg-red-500 text-white' 
                            : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                        }`}>
                          {label}
                        </span>
                      </label>
                    ))}
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-300 mb-3">Price Range ($)</h4>
                    <div className="flex items-center gap-2 mb-3">
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={filters.priceRange.min}
                        onChange={(e) => handlePriceRangeChange('min', parseInt(e.target.value))}
                        className="w-20 px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm focus:outline-none focus:border-red-500"
                      />
                      <span className="text-gray-400">-</span>
                      <input
                        type="number"
                        min="0"
                        max="100"
                        value={filters.priceRange.max}
                        onChange={(e) => handlePriceRangeChange('max', parseInt(e.target.value))}
                        className="w-20 px-2 py-1 bg-gray-700 border border-gray-600 rounded text-white text-sm focus:outline-none focus:border-red-500"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Games Grid */}
          <div className="flex-1">
            {filteredGames.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-16">
                <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mb-4">
                  <FaFilter className="text-2xl text-gray-400" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">No games found</h3>
                <p className="text-gray-400">Try adjusting your filters or check back later</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredGames.map(game => (
                  <div key={game.id} className="h-full">
                    <GameCard game={game} />
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryPage;
