const express = require('express');
const router = express.Router();
const userController = require('../controllers/user.controller');
const { authenticateToken, optionalAuth } = require('../middleware/authMiddleware');
const avatarUpload = require('../middleware/avatarUpload');

// Log all requests to this router
router.use((req, res, next) => {

  next();
});

// Profile routes - require authentication
router.get('/profile', authenticateToken, (req, res, next) => {

  next();
}, userController.getProfile);

router.put('/profile', authenticateToken, (req, res, next) => {
  
  next();
}, userController.updateProfile);

// Avatar upload route
router.post('/avatar', authenticateToken, avatarUpload.single('avatar'), userController.uploadAvatar);

// Public user profile routes
router.get('/:userId/profile', optionalAuth, (req, res, next) => {
  
  next();
}, userController.getPublicProfile);

router.get('/:userId/reviews', userController.getUserReviews);
router.get('/:userId/posts', userController.getUserPosts);

module.exports = router;
